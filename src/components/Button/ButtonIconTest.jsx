import React from 'react';
import Button from './Button.jsx';
import { CheckIcon, CloseIcon, EditIcon, SearchIcon } from '../../assets/IconLibrary.jsx';

const ButtonIconTest = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: '#1F252B' }}>
      <h2 style={{ color: 'white', marginBottom: '20px' }}>Button Icon Test</h2>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        <div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>Primary Buttons</h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Button variant="primary" icon={<CheckIcon />}>Save</Button>
            <Button variant="primary" icon={<CheckIcon />} disabled>Disabled</Button>
          </div>
        </div>
        
        <div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>Secondary Buttons</h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Button variant="secondary" icon={<EditIcon />}>Edit</Button>
            <Button variant="secondary" icon={<EditIcon />} disabled>Disabled</Button>
          </div>
        </div>
        
        <div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>Tertiary Buttons</h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Button variant="tertiary" icon={<CloseIcon />}>Cancel</Button>
            <Button variant="tertiary" icon={<CloseIcon />} disabled>Disabled</Button>
          </div>
        </div>
        
        <div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>Ghost Buttons</h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Button variant="ghost" icon={<SearchIcon />}>Search</Button>
            <Button variant="ghost" icon={<SearchIcon />} disabled>Disabled</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ButtonIconTest;
