import { useState } from 'react';
import TertiaryTabs from './components/Tab/TertiaryTabs.jsx';
import Button from './components/Button/Button.jsx';
import TagButtonWithPopover from './components/TagButtonWithPopover/TagButtonWithPopover.jsx';
import FillSlider from './components/FillSlider/FillSlider.jsx';
import Tag from './components/Tag/Tag.jsx';
import Toolt<PERSON> from './components/Tooltip/Tooltip.jsx';
import SessionManager from './components/SessionManager/SessionManager.jsx';
import ImageGallery from './components/ImageGallery/ImageGallery.jsx';
import ImageIntensitySlider from './components/ImageIntensitySlider/ImageIntensitySlider.jsx';
import VideoFrameUpload from './components/VideoFrameUpload/VideoFrameUpload.jsx';
import IconButtonToggle from './components/IconButtonToggle/IconButtonToggle.jsx';
import NumberInput from './components/NumberInput/NumberInput.jsx';
import TextField from './components/TextField/TextField.jsx';
import Checkbox from './components/Checkbox/Checkbox.jsx';
import GalleryImageCard from './components/GalleryImageCard/GalleryImageCard.jsx';
import ButtonIconTest from './components/Button/ButtonIconTest.jsx';
import IconColorTest from './components/Button/IconColorTest.jsx';
import AIImageGenerator from './components/AIImageGenerator/AIImageGenerator.jsx';
import AspectRatioSelector from './components/AspectRatioSelector/AspectRatioSelector.jsx';
import AIControls from './components/AIControls/AIControls.jsx';
// Import icons for testing
import {
  CheckIcon,
  CloseIcon,
  DeleteIcon,
  DownloadIcon,
  EditIcon,
  HomeIcon,
  PlusIcon,
  SearchIcon
} from './assets/IconLibrary.jsx';

const tabData = ['Tab 1', 'Tab 2', 'Tab 3'];

// Example icons for IconButtonToggle
const ExampleIcon1 = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M12 2L13.09 8.26L19 7L14.74 12L19 17L13.09 15.74L12 22L10.91 15.74L5 17L9.26 12L5 7L10.91 8.26L12 2Z"/>
    </svg>
  );
};

const ExampleIcon2 = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z"/>
    </svg>
  );
};

const ExampleIcon3 = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z"/>
    </svg>
  );
};

// Sample overflow menu items
const samplePrompts = [
  { id: 'group1', type: 'group', label: 'Common Prompts' },
  { id: 'landscape', label: 'Beautiful landscape with mountains' },
  { id: 'portrait', label: 'Professional portrait photo' },
  { id: 'abstract', label: 'Abstract digital art' },
  { id: 'group2', type: 'group', label: 'AI Art Styles' },
  { id: 'photorealistic', label: 'Photorealistic rendering' },
  { id: 'watercolor', label: 'Watercolor painting style' },
  { id: 'cyberpunk', label: 'Cyberpunk aesthetic' },
  { id: 'minimalist', label: 'Minimalist design' },
  { id: 'vintage', label: 'Vintage photography style' },
  { id: 'disabled', label: 'Disabled option', disabled: true }
];

// Sample gallery data
const galleryItems = [
  {
    id: 1,
    type: 'image',
    imageUrl: 'https://picsum.photos/72/72?random=1',
    prompt: 'A beautiful sunset over mountains',
    isProcessing: false
  },
  {
    id: 2,
    type: 'image',
    imageUrl: 'https://picsum.photos/72/72?random=2',
    prompt: 'Abstract digital art with vibrant colors and flowing geometric patterns that blend seamlessly across the canvas',
    isProcessing: true
  },
  {
    id: 3,
    type: 'video',
    imageUrl: 'https://picsum.photos/72/72?random=3',
    prompt: 'Animated sequence of flowing water',
    isProcessing: false
  },
  {
    id: 4,
    type: 'image',
    imageUrl: null,
    prompt: 'Futuristic cityscape at night',
    isProcessing: true
  },
  {
    id: 5,
    type: 'video',
    imageUrl: 'https://picsum.photos/72/72?random=5',
    prompt: 'Time-lapse of clouds moving across sky',
    isProcessing: false
  }
];

function App() {
  // Checkbox state management
  const [checkbox1, setCheckbox1] = useState(false);
  const [checkbox2, setCheckbox2] = useState(true);
  const [checkbox4, setCheckbox4] = useState(false);
  const [checkbox5, setCheckbox5] = useState(true);
  const [checkbox6, setCheckbox6] = useState(false);
  const [checkbox7, setCheckbox7] = useState(false);
  const [checkbox8, setCheckbox8] = useState(true);
  const [checkbox9, setCheckbox9] = useState(false);

  // Gallery Image Card state management
  const [selectedGalleryCards, setSelectedGalleryCards] = useState(new Set([3])); // Card 3 pre-selected
  const [checkedGalleryCards, setCheckedGalleryCards] = useState(new Set([3])); // Card 3 pre-checked
  const [galleryCardFeedbacks, setGalleryCardFeedbacks] = useState({ 3: 'up', 4: 'down' });

  // Table/Group checkbox example - demonstrates proper indeterminate usage
  const [tableRows, setTableRows] = useState([
    { id: 1, selected: false, name: 'Row 1' },
    { id: 2, selected: true, name: 'Row 2' },
    { id: 3, selected: false, name: 'Row 3' },
    { id: 4, selected: true, name: 'Row 4' },
  ]);

  // Calculate select-all checkbox state
  const selectedCount = tableRows.filter(row => row.selected).length;
  const isAllSelected = selectedCount === tableRows.length;
  const isIndeterminate = selectedCount > 0 && selectedCount < tableRows.length;

  const handleSelectAll = (e) => {
    const newSelected = e.target.checked;
    setTableRows(rows => rows.map(row => ({ ...row, selected: newSelected })));
  };

  const handleRowSelect = (rowId, checked) => {
    setTableRows(rows => rows.map(row =>
      row.id === rowId ? { ...row, selected: checked } : row
    ));
  };

  // Gallery Image Card handlers
  const handleGalleryCardSelect = (cardId) => {
    const newSelected = new Set(selectedGalleryCards);
    const newChecked = new Set(checkedGalleryCards);

    if (newSelected.has(cardId)) {
      // Deselecting: remove from both selected and checked
      newSelected.delete(cardId);
      newChecked.delete(cardId);
    } else {
      // Selecting: add to both selected and checked
      newSelected.add(cardId);
      newChecked.add(cardId);
    }

    setSelectedGalleryCards(newSelected);
    setCheckedGalleryCards(newChecked);
  };

  const handleGalleryCardCheckbox = (cardId) => (e) => {
    const newSelected = new Set(selectedGalleryCards);
    const newChecked = new Set(checkedGalleryCards);

    if (e.target.checked) {
      // Checking: add to both selected and checked
      newSelected.add(cardId);
      newChecked.add(cardId);
    } else {
      // Unchecking: remove from both selected and checked
      newSelected.delete(cardId);
      newChecked.delete(cardId);
    }

    setSelectedGalleryCards(newSelected);
    setCheckedGalleryCards(newChecked);
  };

  const handleGalleryCardFeedback = (cardId) => (feedback) => {
    setGalleryCardFeedbacks(prev => ({
      ...prev,
      [cardId]: feedback
    }));
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>Hello World</h1>

      <div style={{ marginBottom: '20px' }}>
        <h2>AI Controls</h2>
        <AIControls
          mode="image"
          onModeChange={(mode) => console.log('Mode changed to:', mode)}
          onGenerate={(params) => console.log('Generate with:', params)}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Aspect Ratio Selector</h2>
        <AspectRatioSelector
          selectedRatio="1:1"
          onRatioChange={(ratio) => console.log('Aspect ratio changed to:', ratio)}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Image Intensity Slider</h2>
        <ImageIntensitySlider
          onIntensityChange={(intensity) => console.log('Intensity:', intensity)}
          onImageChange={(file, dataUrl) => console.log('Image changed:', file?.name, dataUrl ? 'loaded' : 'removed')}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Video Frame Upload</h2>
        <VideoFrameUpload
          onFirstFrameChange={(file, dataUrl) => console.log('First frame changed:', file?.name, dataUrl ? 'loaded' : 'removed')}
          onLastFrameChange={(file, dataUrl) => console.log('Last frame changed:', file?.name, dataUrl ? 'loaded' : 'removed')}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Icon Button Toggle</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px', alignItems: 'flex-start' }}>
          <div>
            <h3>Horizontal Layout</h3>
            <IconButtonToggle
              icons={[ExampleIcon1, ExampleIcon2, ExampleIcon3]}
              layout="horizontal"
              selectedIndex={0}
              onChange={(index) => console.log('Horizontal toggle changed to:', index)}
            />
          </div>
          <div>
            <h3>Vertical Layout</h3>
            <IconButtonToggle
              icons={[ExampleIcon1, ExampleIcon2, ExampleIcon3]}
              layout="vertical"
              selectedIndex={1}
              onChange={(index) => console.log('Vertical toggle changed to:', index)}
            />
          </div>
          <div>
            <h3>More Icons (Horizontal)</h3>
            <IconButtonToggle
              icons={[ExampleIcon1, ExampleIcon2, ExampleIcon3, ExampleIcon1, ExampleIcon2]}
              layout="horizontal"
              selectedIndex={2}
              onChange={(index) => console.log('Multi-icon toggle changed to:', index)}
            />
          </div>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Number Input</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '15px', alignItems: 'flex-start' }}>
          <NumberInput
            label="Seed"
            initialValue={42}
            min={0}
            max={999999}
            onChange={(value) => console.log('Seed value:', value)}
          />
          <NumberInput
            label="Steps"
            initialValue={20}
            min={1}
            max={100}
            onChange={(value) => console.log('Steps value:', value)}
          />
          <NumberInput
            label="Scale"
            initialValue={7}
            min={1}
            max={20}
            onChange={(value) => console.log('Scale value:', value)}
          />
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Text Field</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px', alignItems: 'flex-start' }}>
          <div>
            <h3>Default with Label</h3>
            <TextField
              label="Description"
              placeholder="Enter your description..."
              onChange={(value) => console.log('Description:', value)}
            />
          </div>
          <div>
            <h3>Without Label</h3>
            <TextField
              showLabel={false}
              placeholder="Enter text without label..."
              onChange={(value) => console.log('No label:', value)}
            />
          </div>
          <div>
            <h3>Draggable Resize</h3>
            <TextField
              label="Notes"
              placeholder="This field can be resized by dragging..."
              draggable={true}
              onChange={(value) => console.log('Draggable:', value)}
            />
          </div>
          <div>
            <h3>Disabled State</h3>
            <TextField
              label="Read-only Field"
              value="This field is disabled and cannot be edited"
              disabled={true}
            />
          </div>
          <div>
            <h3>Filled Disabled State</h3>
            <TextField
              label="Filled Disabled"
              value="This field has content but is disabled"
              disabled={true}
            />
          </div>
          <div>
            <h3>With Overflow Menu (Single Select)</h3>
            <TextField
              label="Prompt"
              placeholder="Select or type a prompt..."
              showOverflowMenu={true}
              overflowMenuItems={samplePrompts}
              multiSelect={false}
              showGroupLabels={true}
              onChange={(value) => console.log('Single select:', value)}
            />
          </div>
          <div>
            <h3>With Overflow Menu (Multi Select)</h3>
            <TextField
              label="Multiple Prompts"
              placeholder="Select multiple prompts..."
              showOverflowMenu={true}
              overflowMenuItems={samplePrompts}
              multiSelect={true}
              showGroupLabels={true}
              onChange={(value) => console.log('Multi select:', value)}
            />
          </div>
          <div>
            <h3>Overflow Menu + Draggable</h3>
            <TextField
              label="Advanced Field"
              placeholder="Resizable field with menu..."
              showOverflowMenu={true}
              overflowMenuItems={samplePrompts}
              draggable={true}
              multiSelect={false}
              showGroupLabels={false}
              onChange={(value) => console.log('Advanced field:', value)}
            />
          </div>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Checkbox</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px', alignItems: 'flex-start' }}>
          <div>
            <h3>Interactive States</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <Checkbox
                label="Default State"
                checked={checkbox1}
                onChange={(e) => setCheckbox1(e.target.checked)}
              />
              <Checkbox
                label="Selected State"
                checked={checkbox2}
                onChange={(e) => setCheckbox2(e.target.checked)}
              />
            </div>
          </div>

          <div>
            <h3>Table Select All Example</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '12px', border: '1px solid #383E46', borderRadius: '4px' }}>
              <Checkbox
                label="Select All Rows"
                checked={isAllSelected}
                indeterminate={isIndeterminate}
                onChange={handleSelectAll}
              />
              <div style={{ marginLeft: '20px', display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {tableRows.map(row => (
                  <Checkbox
                    key={row.id}
                    label={row.name}
                    checked={row.selected}
                    onChange={(e) => handleRowSelect(row.id, e.target.checked)}
                  />
                ))}
              </div>
            </div>
          </div>

          <div>
            <h3>Disabled States</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <Checkbox
                label="Disabled Default"
                checked={false}
                disabled={true}
              />
              <Checkbox
                label="Disabled Selected"
                checked={true}
                disabled={true}
              />
              <Checkbox
                label="Disabled Partial"
                checked={false}
                indeterminate={true}
                disabled={true}
              />
            </div>
          </div>

          <div>
            <h3>Without Labels</h3>
            <div style={{ display: 'flex', flexDirection: 'row', gap: '12px', alignItems: 'center' }}>
              <Checkbox
                showLabel={false}
                checked={checkbox4}
                onChange={(e) => setCheckbox4(e.target.checked)}
              />
              <Checkbox
                showLabel={false}
                checked={checkbox5}
                onChange={(e) => setCheckbox5(e.target.checked)}
              />
              <Checkbox
                showLabel={false}
                checked={checkbox6}
                indeterminate={true}
                onChange={(e) => setCheckbox6(e.target.checked)}
              />
            </div>
          </div>

          <div>
            <h3>Custom Labels</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <Checkbox
                label="Accept terms and conditions"
                checked={checkbox7}
                onChange={(e) => setCheckbox7(e.target.checked)}
              />
              <Checkbox
                label="Subscribe to newsletter"
                checked={checkbox8}
                onChange={(e) => setCheckbox8(e.target.checked)}
              />
              <Checkbox
                label="Enable notifications"
                checked={checkbox9}
                indeterminate={true}
                onChange={(e) => setCheckbox9(e.target.checked)}
              />
            </div>
          </div>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Image Gallery</h2>
        <ImageGallery items={galleryItems} />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Gallery Image Cards</h2>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(216px, 1fr))',
          gap: '16px',
          maxWidth: '1200px'
        }}>
          <GalleryImageCard
            type="image"
            imageUrl="https://picsum.photos/200/200?random=10"
            fileName="sunset_landscape.png"
            fileSize={2048576}
            createdAt={new Date('2025-01-15T14:30:00Z')}
            isSelected={selectedGalleryCards.has(1)}
            isChecked={checkedGalleryCards.has(1)}
            feedback={galleryCardFeedbacks[1]}
            onSelect={() => handleGalleryCardSelect(1)}
            onCheckboxChange={handleGalleryCardCheckbox(1)}
            onFeedback={handleGalleryCardFeedback(1)}
            onRestyle={() => console.log('Restyle image')}
            onVideoAI={() => console.log('Video AI')}
            onDownload={() => console.log('Download image')}
          />
          <GalleryImageCard
            type="video"
            imageUrl="https://picsum.photos/200/200?random=11"
            videoUrl="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
            fileName="animation_sequence.mov"
            fileSize={5242880}
            createdAt={new Date('2025-01-15T16:45:00Z')}
            duration={5}
            isSelected={selectedGalleryCards.has(2)}
            isChecked={checkedGalleryCards.has(2)}
            feedback={galleryCardFeedbacks[2]}
            onSelect={() => handleGalleryCardSelect(2)}
            onCheckboxChange={handleGalleryCardCheckbox(2)}
            onFeedback={handleGalleryCardFeedback(2)}
            onRestyle={() => console.log('Restyle video')}
            onVideoAI={() => console.log('Video AI')}
            onDownload={() => console.log('Download video')}
          />
          <GalleryImageCard
            type="image"
            imageUrl="https://picsum.photos/200/200?random=12"
            fileName="abstract_art.png"
            fileSize={1536000}
            createdAt={new Date('2025-01-14T09:15:00Z')}
            isSelected={selectedGalleryCards.has(3)}
            isChecked={checkedGalleryCards.has(3)}
            feedback={galleryCardFeedbacks[3]}
            onSelect={() => handleGalleryCardSelect(3)}
            onCheckboxChange={handleGalleryCardCheckbox(3)}
            onFeedback={handleGalleryCardFeedback(3)}
            onRestyle={() => console.log('Restyle selected')}
            onVideoAI={() => console.log('Video AI selected')}
            onDownload={() => console.log('Download selected')}
          />
          <GalleryImageCard
            type="video"
            imageUrl="https://picsum.photos/200/200?random=13"
            videoUrl="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"
            fileName="flowing_water.mov"
            fileSize={8388608}
            createdAt={new Date('2025-01-13T11:20:00Z')}
            duration={77}
            isSelected={selectedGalleryCards.has(4)}
            isChecked={checkedGalleryCards.has(4)}
            feedback={galleryCardFeedbacks[4]}
            onSelect={() => handleGalleryCardSelect(4)}
            onCheckboxChange={handleGalleryCardCheckbox(4)}
            onFeedback={handleGalleryCardFeedback(4)}
            onRestyle={() => console.log('Restyle video feedback')}
            onVideoAI={() => console.log('Video AI feedback')}
            onDownload={() => console.log('Download video feedback')}
          />
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Session Manager</h2>
        <SessionManager />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Tertiary Tabs</h2>
        <TertiaryTabs tabs={tabData} />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Tag Buttons</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
          <Button variant="tag">Tag 1</Button>
          <Button variant="tag">Tag 2</Button>
          <Button variant="tag">Longer Tag Name</Button>
          <Button variant="tag">Short</Button>
          <Button variant="tag">Category</Button>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Aspect Ratio Popover</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
          <TagButtonWithPopover initialRatio="16:9" />
          <TagButtonWithPopover initialRatio="1:1" />
          <TagButtonWithPopover initialRatio="4:3" />
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Fill Slider</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '15px', alignItems: 'flex-start' }}>
          <FillSlider
            initialValue={0}
            label="Progress"
            onChange={(value) => console.log('Slider 1:', value)}
          />
          <FillSlider
            initialValue={34}
            label="Completion"
            onChange={(value) => console.log('Slider 2:', value)}
          />
          <FillSlider
            initialValue={67}
            label="Loading"
            onChange={(value) => console.log('Slider 3:', value)}
          />
          <FillSlider
            initialValue={100}
            label="Status"
            onChange={(value) => console.log('Slider 4:', value)}
          />
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Status Tags</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
          <Tag variant="light">Light</Tag>
          <Tag variant="dark">Dark</Tag>
          <Tag variant="info">Info</Tag>
          <Tag variant="error">Error</Tag>
          <Tag variant="warning">Warning</Tag>
          <Tag variant="success">Success</Tag>
        </div>

        <div style={{ marginTop: '15px', display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
          <Tag variant="light">Available</Tag>
          <Tag variant="dark">Processing</Tag>
          <Tag variant="info">New Feature</Tag>
          <Tag variant="error">Failed</Tag>
          <Tag variant="warning">Pending</Tag>
          <Tag variant="success">Completed</Tag>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Tooltips Demo</h2>
        <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap', alignItems: 'center', padding: '40px' }}>
          <Tooltip
            content="This is a primary button used for main actions in the interface."
            label="Primary Action"
          >
            <Button variant="primary">Primary</Button>
          </Tooltip>

          <Tooltip
            content="Secondary buttons are used for alternative actions or less important operations."
            label="Secondary Action"
          >
            <Button variant="secondary">Secondary</Button>
          </Tooltip>

          <Tooltip
            content="Use this slider to adjust the completion percentage of your task."
            label="Progress Control"
          >
            <FillSlider initialValue={45} label="Task Progress" />
          </Tooltip>

          <Tooltip
            content="Status tags provide quick visual feedback about the current state."
            label="Status Indicator"
          >
            <Tag variant="success">Completed</Tag>
          </Tooltip>

          <Tooltip
            content="This popover allows you to select different aspect ratios for your content."
            label="Aspect Ratio Selector"
          >
            <TagButtonWithPopover initialRatio="16:9" />
          </Tooltip>
        </div>

        {/* Test tooltips for left/right positioning */}
        <div style={{ display: 'flex', justifyContent: 'space-between', padding: '40px', marginTop: '40px' }}>
          <Tooltip
            content="This tooltip should appear to the right of this button when there's space."
            label="Right Tooltip"
            position="right"
          >
            <Button variant="primary">Left Side</Button>
          </Tooltip>

          <Tooltip
            content="This tooltip should appear to the left of this button when there's space."
            label="Left Tooltip"
            position="left"
          >
            <Button variant="secondary">Right Side</Button>
          </Tooltip>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>New/Add Buttons</h2>
        <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap', alignItems: 'center', padding: '20px' }}>
          <Button variant="new">New Session</Button>
          <Button variant="add">Add Item</Button>
          <Button variant="new" />
          <Button variant="add" />
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Icon Color Debug Test</h2>
        <IconColorTest />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Button Icon Test Component</h2>
        <ButtonIconTest />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Buttons with Icons</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '15px', alignItems: 'flex-start' }}>
          <div>
            <h3>Primary Buttons with Icons</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
              <Button variant="primary" icon={<CheckIcon />}>Save</Button>
              <Button variant="primary" icon={<DownloadIcon />}>Download</Button>
              <Button variant="primary" icon={<PlusIcon />} iconPosition="right">Add New</Button>
            </div>
          </div>

          <div>
            <h3>Amazon Primary Buttons with Icons</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
              <Button variant="amazon-primary" icon={<CheckIcon />}>Save</Button>
              <Button variant="amazon-primary" icon={<DownloadIcon />}>Download</Button>
              <Button variant="amazon-primary" icon={<PlusIcon />} iconPosition="right">Add New</Button>
            </div>
          </div>

          <div>
            <h3>Secondary Buttons with Icons</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
              <Button variant="secondary" icon={<EditIcon />}>Edit</Button>
              <Button variant="secondary" icon={<SearchIcon />}>Search</Button>
              <Button variant="secondary" icon={<HomeIcon />} iconPosition="right">Go Home</Button>
            </div>
          </div>

          <div>
            <h3>Tertiary Buttons with Icons</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
              <Button variant="tertiary" icon={<CloseIcon />}>Cancel</Button>
              <Button variant="tertiary" icon={<DeleteIcon />}>Delete</Button>
              <Button variant="tertiary" icon={<EditIcon />} iconPosition="right">Settings</Button>
            </div>
          </div>

          <div>
            <h3>Ghost Buttons with Icons</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
              <Button variant="ghost" icon={<CheckIcon />}>Approve</Button>
              <Button variant="ghost" icon={<CloseIcon />}>Dismiss</Button>
              <Button variant="ghost" icon={<SearchIcon />} iconPosition="right">Find More</Button>
            </div>
          </div>

          <div>
            <h3>Disabled Buttons with Icons</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
              <Button variant="primary" icon={<CheckIcon />} disabled>Disabled Primary</Button>
              <Button variant="amazon-primary" icon={<DownloadIcon />} disabled>Disabled Amazon Primary</Button>
              <Button variant="secondary" icon={<EditIcon />} disabled>Disabled Secondary</Button>
              <Button variant="tertiary" icon={<CloseIcon />} disabled>Disabled Tertiary</Button>
              <Button variant="ghost" icon={<SearchIcon />} disabled>Disabled Ghost</Button>
            </div>
          </div>

          <div>
            <h3>Color Inheritance Test</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
              <Button variant="primary" icon={CheckIcon}>Primary with Function Icon</Button>
              <Button variant="amazon-primary" icon={<DownloadIcon />}>Amazon Primary with Element Icon</Button>
              <Button variant="secondary" icon={<EditIcon />}>Secondary with Element Icon</Button>
              <Button variant="tertiary" icon={CloseIcon}>Tertiary with Function Icon</Button>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h2>Button Comparison (No Icons)</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="tertiary">Tertiary</Button>
          <Button variant="tag">Tag Style</Button>
          <Button variant="ghost">Ghost</Button>
        </div>
      </div>
    </div>
  );
}

export default App;