import React from 'react';
import Button from './Button.jsx';
import styles from './Button.module.css';

// Test icon with hardcoded fill (like our SVG files)
const TestIconWithFill = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16.5 8.02501L10.14 14.385L7.485 11.73C7.1925 11.4375 6.72 11.4375 6.4275 11.73C6.135 12.0225 6.135 12.495 6.4275 12.7875L9.60751 15.9675C9.90001 16.26 10.3725 16.26 10.665 15.9675L17.55 9.08251C17.8425 8.79001 17.8425 8.31001 17.5425 8.01751C17.265 7.73251 16.7925 7.73251 16.5 8.02501Z" fill="#C8CDD5"/>
  </svg>
);

// Test icon without hardcoded fill
const TestIconWithoutFill = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16.5 8.02501L10.14 14.385L7.485 11.73C7.1925 11.4375 6.72 11.4375 6.4275 11.73C6.135 12.0225 6.135 12.495 6.4275 12.7875L9.60751 15.9675C9.90001 16.26 10.3725 16.26 10.665 15.9675L17.55 9.08251C17.8425 8.79001 17.8425 8.31001 17.5425 8.01751C17.265 7.73251 16.7925 7.73251 16.5 8.02501Z"/>
  </svg>
);

// Test icon with currentColor
const TestIconWithCurrentColor = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16.5 8.02501L10.14 14.385L7.485 11.73C7.1925 11.4375 6.72 11.4375 6.4275 11.73C6.135 12.0225 6.135 12.495 6.4275 12.7875L9.60751 15.9675C9.90001 16.26 10.3725 16.26 10.665 15.9675L17.55 9.08251C17.8425 8.79001 17.8425 8.31001 17.5425 8.01751C17.265 7.73251 16.7925 7.73251 16.5 8.02501Z" fill="currentColor"/>
  </svg>
);

const IconColorTest = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: '#1F252B' }}>
      <h2 style={{ color: 'white', marginBottom: '20px' }}>Icon Color Test</h2>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        <div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>Icons with Hardcoded Fill (#C8CDD5)</h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Button variant="primary" icon={<TestIconWithFill />}>Primary</Button>
            <Button variant="secondary" icon={<TestIconWithFill />}>Secondary</Button>
            <Button variant="tertiary" icon={<TestIconWithFill />}>Tertiary</Button>
          </div>
        </div>
        
        <div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>Icons without Fill Attribute</h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Button variant="primary" icon={<TestIconWithoutFill />}>Primary</Button>
            <Button variant="secondary" icon={<TestIconWithoutFill />}>Secondary</Button>
            <Button variant="tertiary" icon={<TestIconWithoutFill />}>Tertiary</Button>
          </div>
        </div>
        
        <div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>Icons with fill="currentColor"</h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Button variant="primary" icon={<TestIconWithCurrentColor />}>Primary</Button>
            <Button variant="secondary" icon={<TestIconWithCurrentColor />}>Secondary</Button>
            <Button variant="tertiary" icon={<TestIconWithCurrentColor />}>Tertiary</Button>
          </div>
        </div>
        
        <div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>Direct Icon Test (no button)</h3>
          <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
            <div style={{ color: '#78AEFF' }}>
              <span>Blue text: </span>
              <TestIconWithFill className={styles.icon} />
              <TestIconWithoutFill className={styles.icon} />
              <TestIconWithCurrentColor className={styles.icon} />
            </div>
            <div style={{ color: '#C8CDD5' }}>
              <span>Gray text: </span>
              <TestIconWithFill className={styles.icon} />
              <TestIconWithoutFill className={styles.icon} />
              <TestIconWithCurrentColor className={styles.icon} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IconColorTest;
